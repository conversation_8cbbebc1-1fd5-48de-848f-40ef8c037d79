<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间轴滚动修复测试</title>
    <link rel="stylesheet" href="../../src/styles/gantt.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .test-controls {
            padding: 20px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .control-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .control-section h3 {
            margin: 0 0 15px 0;
            color: #374151;
            font-size: 16px;
        }

        .test-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .test-btn {
            padding: 10px 15px;
            border: 1px solid #6366f1;
            border-radius: 6px;
            background: white;
            color: #6366f1;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .test-btn:hover {
            background: #6366f1;
            color: white;
        }

        .status-display {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            font-size: 13px;
            color: #6b7280;
        }

        .status-value {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            font-family: monospace;
        }

        .gantt-container {
            height: 500px;
            position: relative;
            border: 2px solid #6366f1;
        }

        .info-section {
            padding: 20px;
            background: #f1f5f9;
            border-top: 1px solid #e2e8f0;
        }

        .info-section h3 {
            margin: 0 0 15px 0;
            color: #374151;
        }

        .fix-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .fix-list li {
            padding: 8px 0;
            color: #6b7280;
            font-size: 14px;
            border-bottom: 1px solid #f3f4f6;
        }

        .fix-list li:last-child {
            border-bottom: none;
        }

        .fix-list li::before {
            content: "🔧";
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 时间轴滚动修复测试</h1>
            <p>测试横向滚动时时间轴虚拟化渲染的修复效果</p>
        </div>

        <div class="test-controls">
            <div class="control-section">
                <h3>🧪 滚动测试</h3>
                <div class="test-buttons">
                    <button class="test-btn" onclick="testSmoothScroll()">平滑滚动测试</button>
                    <button class="test-btn" onclick="testFastScroll()">快速滚动测试</button>
                    <button class="test-btn" onclick="testEdgeScroll()">边界滚动测试</button>
                    <button class="test-btn" onclick="testZoomScroll()">缩放滚动测试</button>
                </div>
            </div>

            <div class="control-section">
                <h3>📊 性能监控</h3>
                <div class="status-display">
                    <div class="status-item">
                        <span class="status-label">可视刻度数</span>
                        <span class="status-value" id="visibleScales">-</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">渲染时间</span>
                        <span class="status-value" id="renderTime">-</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">滚动位置</span>
                        <span class="status-value" id="scrollPosition">-</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">缩放级别</span>
                        <span class="status-value" id="zoomLevel">-</span>
                    </div>
                </div>
            </div>

            <div class="control-section">
                <h3>⚙️ 视图控制</h3>
                <div class="test-buttons">
                    <button class="test-btn" onclick="changeViewMode('day')">日视图</button>
                    <button class="test-btn" onclick="changeViewMode('week')">周视图</button>
                    <button class="test-btn" onclick="changeViewMode('month')">月视图</button>
                    <button class="test-btn" onclick="resetView()">重置视图</button>
                </div>
            </div>
        </div>

        <div class="gantt-container" id="ganttContainer"></div>

        <div class="info-section">
            <h3>修复内容</h3>
            <ul class="fix-list">
                <li>修复TimeScale中二分查找的边界处理错误</li>
                <li>改进可视区域刻度计算，避免遗漏边缘元素</li>
                <li>优化刻度宽度计算，确保精确对齐</li>
                <li>改进滚动防抖机制，使用requestAnimationFrame</li>
                <li>同步时间轴容器宽度与内容宽度</li>
                <li>增加视口信息实时更新，确保虚拟化计算准确</li>
            </ul>
        </div>
    </div>

    <script type="module">
        import GanttChart from '../../src/GanttChart.js';
        import DateUtils from '../../src/utils/DateUtils.js';

        // 生成大量测试数据以测试虚拟化
        function generateLargeTestData() {
            const tasks = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 50; i++) {
                const taskStart = DateUtils.addDays(startDate, i * 3);
                const duration = 5 + Math.random() * 20;
                const taskEnd = DateUtils.addDays(taskStart, duration);
                
                tasks.push({
                    id: `scroll-test-${i}`,
                    name: `滚动测试任务 ${i + 1}`,
                    startDate: taskStart,
                    endDate: taskEnd,
                    progress: Math.random()
                });
            }
            
            return tasks;
        }

        // 初始化甘特图
        let ganttInstance = null;

        function initializeGantt() {
            const container = document.getElementById('ganttContainer');
            container.innerHTML = '';
            
            const testData = generateLargeTestData();
            
            ganttInstance = new GanttChart('ganttContainer', {
                data: testData,
                viewMode: 'day',
                pixelsPerDay: 40,
                theme: {
                    mode: 'light'
                },
                timeline: {
                    showWeekday: true,
                    adaptiveWidth: true,
                    minScaleWidth: 30,
                    maxScaleWidth: 120
                },
                taskList: {
                    columns: [
                        { key: 'name', title: '任务名称', width: 200 },
                        { key: 'progress', title: '进度', width: 80 }
                    ]
                }
            });

            // 监听滚动事件以更新状态
            ganttInstance.on('scroll', updateScrollStatus);
            ganttInstance.on('zoom_change', updateZoomStatus);
            ganttInstance.on('ready', () => {
                updateStatus();
                console.log('甘特图初始化完成，开始滚动测试...');
            });
        }

        // 更新状态显示
        function updateStatus() {
            if (!ganttInstance) return;

            const stats = ganttInstance.getStats();
            document.getElementById('visibleScales').textContent = stats.visibleScales || '-';
            document.getElementById('renderTime').textContent = `${stats.renderTime || 0}ms`;
            document.getElementById('zoomLevel').textContent = `${Math.round((ganttInstance.state.zoomLevel || 1) * 100)}%`;
        }

        function updateScrollStatus(scrollData) {
            document.getElementById('scrollPosition').textContent = `${Math.round(scrollData.x)}, ${Math.round(scrollData.y)}`;
        }

        function updateZoomStatus(zoomLevel) {
            document.getElementById('zoomLevel').textContent = `${Math.round(zoomLevel * 100)}%`;
        }

        // 测试函数
        window.testSmoothScroll = function() {
            if (!ganttInstance) return;
            
            const chartBody = ganttInstance.elements.chartBody;
            const maxScroll = chartBody.scrollWidth - chartBody.clientWidth;
            const step = maxScroll / 100;
            let current = 0;
            
            const scroll = () => {
                chartBody.scrollLeft = current;
                current += step;
                if (current <= maxScroll) {
                    requestAnimationFrame(scroll);
                }
            };
            
            scroll();
        };

        window.testFastScroll = function() {
            if (!ganttInstance) return;
            
            const chartBody = ganttInstance.elements.chartBody;
            const maxScroll = chartBody.scrollWidth - chartBody.clientWidth;
            
            // 快速跳跃滚动
            const positions = [0, maxScroll * 0.25, maxScroll * 0.5, maxScroll * 0.75, maxScroll];
            let index = 0;
            
            const fastScroll = () => {
                if (index < positions.length) {
                    chartBody.scrollLeft = positions[index];
                    index++;
                    setTimeout(fastScroll, 200);
                }
            };
            
            fastScroll();
        };

        window.testEdgeScroll = function() {
            if (!ganttInstance) return;
            
            const chartBody = ganttInstance.elements.chartBody;
            const maxScroll = chartBody.scrollWidth - chartBody.clientWidth;
            
            // 测试边界滚动
            chartBody.scrollLeft = 0;
            setTimeout(() => chartBody.scrollLeft = maxScroll, 500);
            setTimeout(() => chartBody.scrollLeft = 0, 1000);
        };

        window.testZoomScroll = function() {
            if (!ganttInstance) return;
            
            // 在不同缩放级别下测试滚动
            const zoomLevels = [0.5, 1.0, 1.5, 2.0];
            let index = 0;
            
            const testZoom = () => {
                if (index < zoomLevels.length) {
                    ganttInstance.setZoom(zoomLevels[index]);
                    setTimeout(() => {
                        window.testSmoothScroll();
                    }, 300);
                    index++;
                    setTimeout(testZoom, 2000);
                }
            };
            
            testZoom();
        };

        window.changeViewMode = function(mode) {
            if (!ganttInstance) return;
            ganttInstance.setViewMode(mode);
            updateStatus();
        };

        window.resetView = function() {
            if (!ganttInstance) return;
            ganttInstance.setZoom(1.0);
            ganttInstance.setViewMode('day');
            ganttInstance.elements.chartBody.scrollLeft = 0;
            updateStatus();
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，初始化时间轴滚动修复测试...');
            initializeGantt();
        });
    </script>
</body>
</html>
